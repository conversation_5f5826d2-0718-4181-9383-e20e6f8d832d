/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// tslint:disable-next-line: no-imports-from-dist
import * as tfOps from '@tensorflow/tfjs-core/dist/ops/ops_for_converter';
import { getParamValue } from './utils';
export const executeOp = (node, tensorMap, context, ops = tfOps) => {
    switch (node.op) {
        case 'ResizeBilinear': {
            const images = getParamValue('images', node, tensorMap, context);
            const size = getParamValue('size', node, tensorMap, context);
            const alignCorners = getParamValue('alignCorners', node, tensorMap, context);
            const halfPixelCenters = getParamValue('halfPixelCenters', node, tensorMap, context);
            return [ops.image.resizeBilinear(images, [size[0], size[1]], alignCorners, halfPixelCenters)];
        }
        case 'ResizeNearestNeighbor': {
            const images = getParamValue('images', node, tensorMap, context);
            const size = getParamValue('size', node, tensorMap, context);
            const alignCorners = getParamValue('alignCorners', node, tensorMap, context);
            const halfPixelCenters = getParamValue('halfPixelCenters', node, tensorMap, context);
            return [ops.image.resizeNearestNeighbor(images, [size[0], size[1]], alignCorners, halfPixelCenters)];
        }
        case 'CropAndResize': {
            const image = getParamValue('image', node, tensorMap, context);
            const boxes = getParamValue('boxes', node, tensorMap, context);
            const boxInd = getParamValue('boxInd', node, tensorMap, context);
            const cropSize = getParamValue('cropSize', node, tensorMap, context);
            const method = getParamValue('method', node, tensorMap, context);
            const extrapolationValue = getParamValue('extrapolationValue', node, tensorMap, context);
            return [ops.image.cropAndResize(image, boxes, boxInd, cropSize, method, extrapolationValue)];
        }
        case 'ImageProjectiveTransformV3': {
            const images = getParamValue('images', node, tensorMap, context);
            const transforms = getParamValue('transforms', node, tensorMap, context);
            const outputShape = getParamValue('outputShape', node, tensorMap, context);
            const fillValue = getParamValue('fillValue', node, tensorMap, context);
            const interpolation = getParamValue('interpolation', node, tensorMap, context);
            const fillMode = getParamValue('fillMode', node, tensorMap, context);
            return [ops.image.transform(images, transforms, interpolation.toLowerCase(), fillMode.toLowerCase(), fillValue, outputShape)];
        }
        default:
            throw TypeError(`Node type ${node.op} is not implemented`);
    }
};
export const CATEGORY = 'image';
//# sourceMappingURL=data:application/json;base64,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