import { Router } from 'express';
import { AuthService } from '../services/auth.service';
import { authenticate } from '../middleware/auth.middleware';
import { z } from 'zod';

const router = Router();
const authService = AuthService.getInstance();

// Validation schemas
const signUpSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  fullName: z.string().min(2),
  role: z.enum(['student', 'admin']).optional(),
});

const signInSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

// Routes
router.post('/signup', async (req, res) => {
  try {
    const { email, password, fullName, role } = signUpSchema.parse(req.body);
    const result = await authService.signUp(email, password, fullName, role);
    res.json(result);
  } catch (error) {
    console.error('Signup error:', error);
    if (error instanceof z.ZodError) {
      res.status(400).json({ error: error.errors });
    } else {
      res.status(500).json({
        error: 'Failed to create user',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
});

router.post('/signin', async (req, res) => {
  try {
    const { email, password } = signInSchema.parse(req.body);
    const result = await authService.signIn(email, password);
    res.json(result);
  } catch (error) {
    console.error('Signin error:', error);
    if (error instanceof z.ZodError) {
      res.status(400).json({ error: error.errors });
    } else {
      res.status(401).json({
        error: 'Invalid credentials',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
});

router.post('/signout', authenticate, async (req, res) => {
  try {
    await authService.signOut();
    res.json({ message: 'Signed out successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to sign out' });
  }
});

router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await authService.getCurrentUser(req.headers.authorization!.split(' ')[1]);
    res.json({ user });
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
});

export default router; 