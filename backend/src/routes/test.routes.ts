import express from 'express';
import { authenticate, requireRole } from '../middleware/auth.middleware';
import { TestService } from '../services/test.service';
import { z } from 'zod';

const router = express.Router();
const testService = TestService.getInstance();

// Validation schemas
const createTestSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  duration_minutes: z.number().min(1),
  start_time: z.string(),
  end_time: z.string(),
  is_published: z.boolean().optional().default(false)
});

const createQuestionSchema = z.object({
  test_id: z.string(),
  type: z.enum(['mcq', 'short_answer', 'long_answer', 'coding']),
  text: z.string().min(1),
  options: z.array(z.string()).optional(),
  correct_answer: z.string().optional(),
  points: z.number().min(1),
  order: z.number().min(1)
});

// Get all tests
router.get('/', authenticate, async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const tests = await testService.getTests(req.user.id, req.user.role);
    res.json(tests);
  } catch (error) {
    console.error('Error fetching tests:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new test (admin only)
router.post('/', authenticate, requireRole(['admin']), async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const testData = createTestSchema.parse(req.body);
    const test = await testService.createTest({
      ...testData,
      created_by: req.user.id
    });

    res.status(201).json(test);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    console.error('Error creating test:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get test details with questions
router.get('/:testId', authenticate, async (req, res) => {
  try {
    const { testId } = req.params;
    const test = await testService.getTestWithQuestions(testId);

    if (!test) {
      return res.status(404).json({ error: 'Test not found' });
    }

    res.json(test);
  } catch (error) {
    console.error('Error fetching test:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update test (admin only)
router.put('/:testId', authenticate, requireRole(['admin']), async (req, res) => {
  try {
    const { testId } = req.params;
    const updates = createTestSchema.partial().parse(req.body);

    const test = await testService.updateTest(testId, updates);
    if (!test) {
      return res.status(404).json({ error: 'Test not found' });
    }

    res.json(test);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    console.error('Error updating test:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete test (admin only)
router.delete('/:testId', authenticate, requireRole(['admin']), async (req, res) => {
  try {
    const { testId } = req.params;
    const success = await testService.deleteTest(testId);

    if (!success) {
      return res.status(404).json({ error: 'Test not found' });
    }

    res.json({ message: 'Test deleted successfully' });
  } catch (error) {
    console.error('Error deleting test:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add question to test (admin only)
router.post('/:testId/questions', authenticate, requireRole(['admin']), async (req, res) => {
  try {
    const { testId } = req.params;
    const questionData = createQuestionSchema.parse({ ...req.body, test_id: testId });

    const question = await testService.addQuestion(questionData);
    res.status(201).json(question);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    console.error('Error adding question:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create test attempt
router.post('/attempts', authenticate, async (req, res) => {
  try {
    const { test_id } = req.body;
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const attempt = await testService.createAttempt(test_id, req.user.id);
    res.status(201).json(attempt);
  } catch (error) {
    if (error instanceof Error && error.message.includes('already has an active attempt')) {
      return res.status(400).json({ error: error.message });
    }
    console.error('Error creating test attempt:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Submit test attempt
router.post('/attempts/:attemptId/submit', authenticate, async (req, res) => {
  try {
    const { attemptId } = req.params;
    const { answers } = req.body;
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const attempt = await testService.submitAttempt(attemptId, answers);
    if (!attempt) {
      return res.status(404).json({ error: 'Attempt not found' });
    }

    res.json({ message: 'Test submitted successfully', attempt });
  } catch (error) {
    console.error('Error submitting test:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Log violation (simplified for mock implementation)
router.post('/violations', authenticate, async (req, res) => {
  try {
    const { attempt_id, type, timestamp } = req.body;
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // For mock implementation, just log the violation
    console.log('Violation logged:', { attempt_id, type, timestamp, user_id: req.user.id });

    const violation = {
      id: `violation_${Date.now()}`,
      attempt_id,
      type,
      timestamp,
      details: 'Violation detected during test'
    };

    res.status(201).json(violation);
  } catch (error) {
    console.error('Error logging violation:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;