import { Test, Question, TestAttempt, Answer } from '../types/database';

export class TestService {
  private static instance: TestService;
  private tests: Test[] = [];
  private questions: Question[] = [];
  private attempts: TestAttempt[] = [];
  private answers: Answer[] = [];

  private constructor() {
    // Initialize with some sample data
    this.initializeSampleData();
  }

  public static getInstance(): TestService {
    if (!TestService.instance) {
      TestService.instance = new TestService();
    }
    return TestService.instance;
  }

  private initializeSampleData() {
    // Sample test
    const sampleTest: Test = {
      id: 'test_1',
      title: 'JavaScript Fundamentals',
      description: 'Basic JavaScript concepts and syntax',
      duration_minutes: 30,
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      is_published: true,
      created_by: 'admin_1',
      created_at: new Date().toISOString(),
      total_points: 10
    };

    // Sample questions
    const sampleQuestions: Question[] = [
      {
        id: 'q1',
        test_id: 'test_1',
        type: 'mcq',
        text: 'What is the correct way to declare a variable in JavaScript?',
        options: ['var x = 5;', 'variable x = 5;', 'v x = 5;', 'declare x = 5;'],
        correct_answer: 'var x = 5;',
        points: 2,
        order: 1
      },
      {
        id: 'q2',
        test_id: 'test_1',
        type: 'mcq',
        text: 'Which of the following is NOT a JavaScript data type?',
        options: ['String', 'Boolean', 'Float', 'Number'],
        correct_answer: 'Float',
        points: 2,
        order: 2
      },
      {
        id: 'q3',
        test_id: 'test_1',
        type: 'short_answer',
        text: 'What does "DOM" stand for?',
        correct_answer: 'Document Object Model',
        points: 3,
        order: 3
      },
      {
        id: 'q4',
        test_id: 'test_1',
        type: 'mcq',
        text: 'How do you create a function in JavaScript?',
        options: ['function myFunction() {}', 'create myFunction() {}', 'def myFunction() {}', 'function = myFunction() {}'],
        correct_answer: 'function myFunction() {}',
        points: 3,
        order: 4
      }
    ];

    this.tests.push(sampleTest);
    this.questions.push(...sampleQuestions);
  }

  // Test management methods
  async createTest(testData: Omit<Test, 'id' | 'created_at' | 'total_points'> & { description?: string }): Promise<Test> {
    const test: Test = {
      ...testData,
      description: testData.description || '',
      id: `test_${Date.now()}`,
      created_at: new Date().toISOString(),
      total_points: 0 // Will be calculated when questions are added
    };

    this.tests.push(test);
    return test;
  }

  async getTests(userId?: string, role?: string): Promise<Test[]> {
    if (role === 'admin') {
      return this.tests;
    }
    // Students only see published tests
    return this.tests.filter(test => test.is_published);
  }

  async getTestById(testId: string): Promise<Test | null> {
    return this.tests.find(test => test.id === testId) || null;
  }

  async updateTest(testId: string, updates: Partial<Test>): Promise<Test | null> {
    const testIndex = this.tests.findIndex(test => test.id === testId);
    if (testIndex === -1) return null;

    this.tests[testIndex] = { ...this.tests[testIndex], ...updates };
    return this.tests[testIndex];
  }

  async deleteTest(testId: string): Promise<boolean> {
    const testIndex = this.tests.findIndex(test => test.id === testId);
    if (testIndex === -1) return false;

    this.tests.splice(testIndex, 1);
    // Also remove associated questions
    this.questions = this.questions.filter(q => q.test_id !== testId);
    return true;
  }

  // Question management methods
  async addQuestion(questionData: Omit<Question, 'id'>): Promise<Question> {
    const question: Question = {
      ...questionData,
      id: `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    this.questions.push(question);
    
    // Update test total points
    await this.updateTestTotalPoints(question.test_id);
    
    return question;
  }

  async getQuestionsByTestId(testId: string): Promise<Question[]> {
    return this.questions
      .filter(q => q.test_id === testId)
      .sort((a, b) => a.order - b.order);
  }

  async updateQuestion(questionId: string, updates: Partial<Question>): Promise<Question | null> {
    const questionIndex = this.questions.findIndex(q => q.id === questionId);
    if (questionIndex === -1) return null;

    const oldTestId = this.questions[questionIndex].test_id;
    this.questions[questionIndex] = { ...this.questions[questionIndex], ...updates };
    
    // Update total points for affected tests
    await this.updateTestTotalPoints(oldTestId);
    if (updates.test_id && updates.test_id !== oldTestId) {
      await this.updateTestTotalPoints(updates.test_id);
    }
    
    return this.questions[questionIndex];
  }

  async deleteQuestion(questionId: string): Promise<boolean> {
    const questionIndex = this.questions.findIndex(q => q.id === questionId);
    if (questionIndex === -1) return false;

    const testId = this.questions[questionIndex].test_id;
    this.questions.splice(questionIndex, 1);
    
    // Update test total points
    await this.updateTestTotalPoints(testId);
    
    return true;
  }

  private async updateTestTotalPoints(testId: string): Promise<void> {
    const testQuestions = this.questions.filter(q => q.test_id === testId);
    const totalPoints = testQuestions.reduce((sum, q) => sum + q.points, 0);
    
    await this.updateTest(testId, { total_points: totalPoints });
  }

  // Test attempt methods
  async createAttempt(testId: string, userId: string): Promise<TestAttempt> {
    // Check if user already has an active attempt
    const existingAttempt = this.attempts.find(
      a => a.test_id === testId && a.user_id === userId && a.status === 'in_progress'
    );

    if (existingAttempt) {
      throw new Error('User already has an active attempt for this test');
    }

    const attempt: TestAttempt = {
      id: `attempt_${Date.now()}_${userId}`,
      test_id: testId,
      user_id: userId,
      started_at: new Date().toISOString(),
      status: 'in_progress'
    };

    this.attempts.push(attempt);
    return attempt;
  }

  async getAttempt(attemptId: string): Promise<TestAttempt | null> {
    return this.attempts.find(a => a.id === attemptId) || null;
  }

  async submitAttempt(attemptId: string, answers: Record<string, string>): Promise<TestAttempt | null> {
    const attemptIndex = this.attempts.findIndex(a => a.id === attemptId);
    if (attemptIndex === -1) return null;

    // Save answers
    for (const [questionId, answerText] of Object.entries(answers)) {
      const question = this.questions.find(q => q.id === questionId);
      if (question) {
        const answer: Answer = {
          id: `answer_${Date.now()}_${questionId}`,
          attempt_id: attemptId,
          question_id: questionId,
          answer_text: answerText,
          is_correct: this.checkAnswer(question, answerText),
          points_earned: this.checkAnswer(question, answerText) ? question.points : 0
        };
        this.answers.push(answer);
      }
    }

    // Calculate score
    const attemptAnswers = this.answers.filter(a => a.attempt_id === attemptId);
    const score = attemptAnswers.reduce((sum, a) => sum + (a.points_earned || 0), 0);

    // Update attempt
    this.attempts[attemptIndex] = {
      ...this.attempts[attemptIndex],
      status: 'submitted',
      submitted_at: new Date().toISOString(),
      score
    };

    return this.attempts[attemptIndex];
  }

  private checkAnswer(question: Question, answerText: string): boolean {
    if (!question.correct_answer) return false;
    
    if (question.type === 'mcq') {
      return answerText.trim() === question.correct_answer.trim();
    } else {
      // For short answer, do a case-insensitive comparison
      return answerText.toLowerCase().trim() === question.correct_answer.toLowerCase().trim();
    }
  }

  async getUserAttempts(userId: string): Promise<TestAttempt[]> {
    return this.attempts.filter(a => a.user_id === userId);
  }

  async getTestWithQuestions(testId: string): Promise<(Test & { questions: Question[] }) | null> {
    const test = await this.getTestById(testId);
    if (!test) return null;

    const questions = await this.getQuestionsByTestId(testId);
    return { ...test, questions };
  }
}
