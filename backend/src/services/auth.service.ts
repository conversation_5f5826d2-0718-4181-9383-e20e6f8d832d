import { User, UserRole } from '../types/database';
import jwt from 'jsonwebtoken';

export class AuthService {
  private static instance: AuthService;
  private readonly JWT_SECRET: string;

  private constructor() {
    this.JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async signUp(email: string, password: string, fullName: string, role: UserRole = 'student'): Promise<{ user: User; token: string }> {
    // For now, create a mock user for testing
    // TODO: Replace with actual Supabase implementation once database is set up
    const mockUser: User = {
      id: `user_${Date.now()}`,
      email,
      full_name: fullName,
      role,
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
    };

    const token = this.generateToken(mockUser);
    return { user: mockUser, token };
  }

  async signIn(email: string, password: string): Promise<{ user: User; token: string }> {
    // For now, create a mock user for testing
    // TODO: Replace with actual Supabase implementation once database is set up

    // Simple validation for demo
    if (password.length < 8) {
      throw new Error('Invalid credentials');
    }

    const mockUser: User = {
      id: `user_${email.replace('@', '_').replace('.', '_')}`,
      email,
      full_name: 'Demo User',
      role: 'student',
      created_at: new Date().toISOString(),
      last_login: new Date().toISOString(),
    };

    const token = this.generateToken(mockUser);
    return { user: mockUser, token };
  }

  async signOut(): Promise<void> {
    // For mock implementation, just return success
    // TODO: Replace with actual Supabase implementation once database is set up
    return Promise.resolve();
  }

  async getCurrentUser(token: string): Promise<User> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as { id: string; email: string; role: UserRole };

      // For mock implementation, return user data from token
      // TODO: Replace with actual database lookup once database is set up
      const mockUser: User = {
        id: decoded.id,
        email: decoded.email,
        full_name: 'Demo User',
        role: decoded.role,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString(),
      };

      return mockUser;
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  private generateToken(user: User): string {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      this.JWT_SECRET,
      { expiresIn: '24h' }
    );
  }
} 